/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-02 04:20:29 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.views;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;

public final class index_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.release();
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!DOCTYPE html>\r\n");
      out.write("<html lang=\"zh-CN\">\r\n");
      out.write("<head>\r\n");
      out.write("    <meta charset=\"UTF-8\">\r\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n");
      out.write("    <title>");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageTitle}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("</title>\r\n");
      out.write("    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css\">\r\n");
      out.write("    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap\" rel=\"stylesheet\">\r\n");
      out.write("    <link rel=\"stylesheet\" href=\"/community-forum/static/css/style.css\">\r\n");
      out.write("</head>\r\n");
      out.write("<body>\r\n");
      out.write("    <!-- 头部导航 -->\r\n");
      out.write("    <header class=\"header\">\r\n");
      out.write("        <div class=\"container\">\r\n");
      out.write("            <nav class=\"navbar\">\r\n");
      out.write("                <a href=\"/community-forum/\" class=\"logo\">社区论坛</a>\r\n");
      out.write("                \r\n");
      out.write("                <div class=\"search-form\">\r\n");
      out.write("                    <form action=\"/community-forum/search\" method=\"get\">\r\n");
      out.write("                        <input type=\"text\" name=\"keyword\" class=\"search-input\" placeholder=\"搜索帖子...\" required>\r\n");
      out.write("                        <button type=\"submit\" class=\"search-btn\">\r\n");
      out.write("                            <i class=\"fas fa-search\"></i>\r\n");
      out.write("                        </button>\r\n");
      out.write("                    </form>\r\n");
      out.write("                </div>\r\n");
      out.write("                \r\n");
      out.write("                <div class=\"nav-links\">\r\n");
      out.write("                    <a href=\"/community-forum/publish\" class=\"btn btn-primary\">发布帖子</a>\r\n");
      out.write("                    \r\n");
      out.write("                    <div id=\"user-area\">\r\n");
      out.write("                        <!-- 用户登录后显示 -->\r\n");
      out.write("                        <div class=\"user-menu\" style=\"display: none;\" id=\"logged-in-menu\">\r\n");
      out.write("                            <div class=\"notification-badge\">\r\n");
      out.write("                                <a href=\"/community-forum/notifications\">\r\n");
      out.write("                                    <i class=\"fas fa-bell\"></i>\r\n");
      out.write("                                </a>\r\n");
      out.write("                                <span class=\"badge\" style=\"display: none;\">0</span>\r\n");
      out.write("                            </div>\r\n");
      out.write("                            \r\n");
      out.write("                            <div class=\"dropdown\">\r\n");
      out.write("                                <div class=\"user-info\">\r\n");
      out.write("                                    <img src=\"\" alt=\"头像\" class=\"avatar\" id=\"user-avatar\">\r\n");
      out.write("                                    <span id=\"user-nickname\"></span>\r\n");
      out.write("                                </div>\r\n");
      out.write("                                \r\n");
      out.write("                                <div class=\"dropdown-content\">\r\n");
      out.write("                                    <a href=\"/community-forum/user/profile\" id=\"user-profile-link\">\r\n");
      out.write("                                        <i class=\"fas fa-user\"></i> 个人中心\r\n");
      out.write("                                    </a>\r\n");
      out.write("                                    <a href=\"/community-forum/user/posts\" id=\"user-posts-link\">\r\n");
      out.write("                                        <i class=\"fas fa-file-alt\"></i> 我的帖子\r\n");
      out.write("                                    </a>\r\n");
      out.write("                                    <a href=\"/community-forum/user/favorites\">\r\n");
      out.write("                                        <i class=\"fas fa-star\"></i> 我的收藏\r\n");
      out.write("                                    </a>\r\n");
      out.write("                                    <a href=\"/community-forum/user/settings\">\r\n");
      out.write("                                        <i class=\"fas fa-cog\"></i> 设置\r\n");
      out.write("                                    </a>\r\n");
      out.write("                                    <a href=\"javascript:void(0);\" onclick=\"logout()\">\r\n");
      out.write("                                        <i class=\"fas fa-sign-out-alt\"></i> 退出\r\n");
      out.write("                                    </a>\r\n");
      out.write("                                </div>\r\n");
      out.write("                            </div>\r\n");
      out.write("                        </div>\r\n");
      out.write("                        \r\n");
      out.write("                        <!-- 用户未登录显示 -->\r\n");
      out.write("                        <div id=\"not-logged-in-menu\">\r\n");
      out.write("                            <a href=\"/community-forum/login\" class=\"btn btn-outline\">登录</a>\r\n");
      out.write("                            <a href=\"/community-forum/register\" class=\"btn btn-primary\">注册</a>\r\n");
      out.write("                        </div>\r\n");
      out.write("                    </div>\r\n");
      out.write("                </div>\r\n");
      out.write("            </nav>\r\n");
      out.write("        </div>\r\n");
      out.write("    </header>\r\n");
      out.write("    \r\n");
      out.write("    <!-- 主要内容区 -->\r\n");
      out.write("    <main class=\"container main\">\r\n");
      out.write("        <!-- 内容区 -->\r\n");
      out.write("        <div class=\"content\">\r\n");
      out.write("            <!-- 帖子列表 -->\r\n");
      out.write("            <div class=\"post-list\" id=\"post-list\">\r\n");
      out.write("                <!-- 帖子将通过JavaScript动态加载 -->\r\n");
      out.write("                <div class=\"loading-spinner\">\r\n");
      out.write("                    <i class=\"fas fa-spinner fa-spin\"></i> 加载中...\r\n");
      out.write("                </div>\r\n");
      out.write("            </div>\r\n");
      out.write("            \r\n");
      out.write("            <!-- 分页 -->\r\n");
      out.write("            <div class=\"pagination\" id=\"pagination\">\r\n");
      out.write("                <!-- 分页将通过JavaScript动态生成 -->\r\n");
      out.write("            </div>\r\n");
      out.write("        </div>\r\n");
      out.write("        \r\n");
      out.write("        <!-- 侧边栏 -->\r\n");
      out.write("        <div class=\"sidebar\">\r\n");
      out.write("            <!-- 分类列表 -->\r\n");
      out.write("            <div class=\"sidebar-section\">\r\n");
      out.write("                <div class=\"sidebar-header\">\r\n");
      out.write("                    <i class=\"fas fa-list\"></i> 分类\r\n");
      out.write("                </div>\r\n");
      out.write("                <div class=\"sidebar-content\">\r\n");
      out.write("                    <ul class=\"category-list\">\r\n");
      out.write("                        ");
      if (_jspx_meth_c_005fforEach_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("                    </ul>\r\n");
      out.write("                </div>\r\n");
      out.write("            </div>\r\n");
      out.write("            \r\n");
      out.write("            <!-- 热门帖子 -->\r\n");
      out.write("            <div class=\"sidebar-section\">\r\n");
      out.write("                <div class=\"sidebar-header\">\r\n");
      out.write("                    <i class=\"fas fa-fire\"></i> 热门帖子\r\n");
      out.write("                </div>\r\n");
      out.write("                <div class=\"sidebar-content\" id=\"hot-posts\">\r\n");
      out.write("                    <!-- 热门帖子将通过JavaScript动态加载 -->\r\n");
      out.write("                    <div class=\"loading-spinner\">\r\n");
      out.write("                        <i class=\"fas fa-spinner fa-spin\"></i> 加载中...\r\n");
      out.write("                    </div>\r\n");
      out.write("                </div>\r\n");
      out.write("            </div>\r\n");
      out.write("            \r\n");
      out.write("            <!-- 社区信息 -->\r\n");
      out.write("            <div class=\"sidebar-section\">\r\n");
      out.write("                <div class=\"sidebar-header\">\r\n");
      out.write("                    <i class=\"fas fa-info-circle\"></i> 社区信息\r\n");
      out.write("                </div>\r\n");
      out.write("                <div class=\"sidebar-content\">\r\n");
      out.write("                    <p>欢迎来到我们的社区论坛！</p>\r\n");
      out.write("                    <p>这里是一个开放、友善的交流平台，希望大家能够在这里分享知识、交流经验。</p>\r\n");
      out.write("                    <p class=\"mt-2\">\r\n");
      out.write("                        <i class=\"fas fa-users\"></i> <span id=\"user-count\">0</span> 位用户\r\n");
      out.write("                    </p>\r\n");
      out.write("                    <p>\r\n");
      out.write("                        <i class=\"fas fa-file-alt\"></i> <span id=\"post-count\">0</span> 篇帖子\r\n");
      out.write("                    </p>\r\n");
      out.write("                    <p>\r\n");
      out.write("                        <i class=\"fas fa-comment\"></i> <span id=\"comment-count\">0</span> 条评论\r\n");
      out.write("                    </p>\r\n");
      out.write("                </div>\r\n");
      out.write("            </div>\r\n");
      out.write("        </div>\r\n");
      out.write("    </main>\r\n");
      out.write("    \r\n");
      out.write("    <!-- 页脚 -->\r\n");
      out.write("    <footer class=\"footer\">\r\n");
      out.write("        <div class=\"container\">\r\n");
      out.write("            <div class=\"footer-content\">\r\n");
      out.write("                <p>&copy; 2023 社区论坛. 保留所有权利.</p>\r\n");
      out.write("            </div>\r\n");
      out.write("        </div>\r\n");
      out.write("    </footer>\r\n");
      out.write("    \r\n");
      out.write("    <script src=\"/community-forum/static/js/main.js\"></script>\r\n");
      out.write("    <script>\r\n");
      out.write("        // 页面加载完成后执行\r\n");
      out.write("        document.addEventListener('DOMContentLoaded', function() {\r\n");
      out.write("            // 检查用户是否已登录\r\n");
      out.write("            const token = localStorage.getItem('token');\r\n");
      out.write("            const user = JSON.parse(localStorage.getItem('user') || '{}');\r\n");
      out.write("            \r\n");
      out.write("            if (token && user) {\r\n");
      out.write("                // 已登录，显示用户菜单\r\n");
      out.write("                document.getElementById('logged-in-menu').style.display = 'flex';\r\n");
      out.write("                document.getElementById('not-logged-in-menu').style.display = 'none';\r\n");
      out.write("                \r\n");
      out.write("                // 设置用户信息\r\n");
      out.write("                document.getElementById('user-avatar').src = user.avatar || '/community-forum/static/images/avatar/default.jpg';\r\n");
      out.write("                document.getElementById('user-nickname').textContent = user.nickname;\r\n");
      out.write("                document.getElementById('user-profile-link').href = '/community-forum/user/' + user.userId;\r\n");
      out.write("                document.getElementById('user-posts-link').href = '/community-forum/user/posts/' + user.userId;\r\n");
      out.write("                \r\n");
      out.write("                // 获取未读通知数\r\n");
      out.write("                updateNotificationCount();\r\n");
      out.write("            } else {\r\n");
      out.write("                // 未登录，显示登录注册按钮\r\n");
      out.write("                document.getElementById('logged-in-menu').style.display = 'none';\r\n");
      out.write("                document.getElementById('not-logged-in-menu').style.display = 'flex';\r\n");
      out.write("            }\r\n");
      out.write("            \r\n");
      out.write("            // 加载帖子列表\r\n");
      out.write("            loadPosts(1);\r\n");
      out.write("            \r\n");
      out.write("            // 加载热门帖子\r\n");
      out.write("            loadHotPosts();\r\n");
      out.write("            \r\n");
      out.write("            // 加载分类帖子数\r\n");
      out.write("            loadCategoryCounts();\r\n");
      out.write("            \r\n");
      out.write("            // 加载社区统计信息\r\n");
      out.write("            loadCommunityStats();\r\n");
      out.write("        });\r\n");
      out.write("        \r\n");
      out.write("        // 加载帖子列表\r\n");
      out.write("        function loadPosts(page) {\r\n");
      out.write("            const postList = document.getElementById('post-list');\r\n");
      out.write("            postList.innerHTML = '<div class=\"loading-spinner\"><i class=\"fas fa-spinner fa-spin\"></i> 加载中...</div>';\r\n");
      out.write("            \r\n");
      out.write("            fetch('/community-forum/api/post/list?pageNum=' + page + '&pageSize=10')\r\n");
      out.write("                .then(response => response.json())\r\n");
      out.write("                .then(data => {\r\n");
      out.write("                    if (data.code === 200) {\r\n");
      out.write("                        const posts = data.data.list;\r\n");
      out.write("                        const total = data.data.total;\r\n");
      out.write("                        const pageNum = data.data.pageNum;\r\n");
      out.write("                        const pageSize = data.data.pageSize;\r\n");
      out.write("                        \r\n");
      out.write("                        // 清空加载中提示\r\n");
      out.write("                        postList.innerHTML = '';\r\n");
      out.write("                        \r\n");
      out.write("                        // 添加帖子\r\n");
      out.write("                        posts.forEach(post => {\r\n");
      out.write("                            postList.appendChild(createPostElement(post));\r\n");
      out.write("                        });\r\n");
      out.write("                        \r\n");
      out.write("                        // 生成分页\r\n");
      out.write("                        generatePagination(pageNum, Math.ceil(total / pageSize));\r\n");
      out.write("                    } else {\r\n");
      out.write("                        postList.innerHTML = '<div class=\"error-message\">加载帖子失败</div>';\r\n");
      out.write("                    }\r\n");
      out.write("                })\r\n");
      out.write("                .catch(error => {\r\n");
      out.write("                    console.error('加载帖子失败:', error);\r\n");
      out.write("                    postList.innerHTML = '<div class=\"error-message\">加载帖子失败</div>';\r\n");
      out.write("                });\r\n");
      out.write("        }\r\n");
      out.write("        \r\n");
      out.write("        // 创建帖子元素\r\n");
      out.write("        function createPostElement(post) {\r\n");
      out.write("            const postElement = document.createElement('div');\r\n");
      out.write("            postElement.className = 'post-card';\r\n");
      out.write("            \r\n");
      out.write("            const createDate = new Date(post.createTime);\r\n");
      out.write("            const formattedDate = createDate.getFullYear() + '-' + String(createDate.getMonth() + 1).padStart(2, '0') + '-' + String(createDate.getDate()).padStart(2, '0');\r\n");
      out.write("            \r\n");
      out.write("            let postHTML = '';\r\n");
      out.write("            postHTML += '<div class=\"post-header\">';\r\n");
      out.write("            postHTML += '<img src=\"' + (post.userAvatar || '/community-forum/static/images/avatar/default.jpg') + '\" alt=\"头像\" class=\"avatar\">';\r\n");
      out.write("            postHTML += '<div>';\r\n");
      out.write("            postHTML += '<a href=\"/community-forum/user/' + post.userId + '\" class=\"user-name\">' + post.nickname + '</a>';\r\n");
      out.write("            postHTML += '<div class=\"post-meta\">';\r\n");
      out.write("            postHTML += formattedDate;\r\n");
      out.write("            postHTML += '<a href=\"/community-forum/category/' + post.categoryId + '\" class=\"post-category\">' + post.categoryName + '</a>';\r\n");
      out.write("            postHTML += '</div>';\r\n");
      out.write("            postHTML += '</div>';\r\n");
      out.write("            postHTML += '</div>';\r\n");
      out.write("            postHTML += '<div class=\"post-content\">';\r\n");
      out.write("            postHTML += '<h2 class=\"post-title\">';\r\n");
      out.write("            postHTML += '<a href=\"/community-forum/post/' + post.postId + '\">' + post.title + '</a>';\r\n");
      out.write("            postHTML += '</h2>';\r\n");
      out.write("            postHTML += '<p class=\"post-excerpt\">' + (post.content.length > 150 ? post.content.substring(0, 150) + '...' : post.content) + '</p>';\r\n");
      out.write("            postHTML += '</div>';\r\n");
      out.write("            postHTML += '<div class=\"post-footer\">';\r\n");
      out.write("            postHTML += '<div class=\"action view-count\">';\r\n");
      out.write("            postHTML += '<i class=\"fas fa-eye\"></i> ' + post.viewCount;\r\n");
      out.write("            postHTML += '</div>';\r\n");
      out.write("            postHTML += '<div class=\"action like-button\" data-post-id=\"' + post.postId + '\">';\r\n");
      out.write("            postHTML += '<i class=\"fas fa-thumbs-up\"></i> <span class=\"like-count\">' + post.likeCount + '</span>';\r\n");
      out.write("            postHTML += '</div>';\r\n");
      out.write("            postHTML += '<div class=\"action\">';\r\n");
      out.write("            postHTML += '<i class=\"fas fa-comment\"></i> ' + post.commentCount;\r\n");
      out.write("            postHTML += '</div>';\r\n");
      out.write("            postHTML += '<div class=\"action favorite-button\" data-post-id=\"' + post.postId + '\">';\r\n");
      out.write("            postHTML += '<i class=\"fas fa-star\"></i> <span class=\"favorite-count\">0</span>';\r\n");
      out.write("            postHTML += '</div>';\r\n");
      out.write("            postHTML += '</div>';\r\n");
      out.write("            \r\n");
      out.write("            postElement.innerHTML = postHTML;\r\n");
      out.write("            return postElement;\r\n");
      out.write("        }\r\n");
      out.write("        \r\n");
      out.write("        // 生成分页\r\n");
      out.write("        function generatePagination(currentPage, totalPages) {\r\n");
      out.write("            const pagination = document.getElementById('pagination');\r\n");
      out.write("            pagination.innerHTML = '';\r\n");
      out.write("            \r\n");
      out.write("            // 上一页\r\n");
      out.write("            const prevLink = document.createElement('a');\r\n");
      out.write("            prevLink.href = 'javascript:void(0);';\r\n");
      out.write("            prevLink.innerHTML = '&laquo;';\r\n");
      out.write("            if (currentPage > 1) {\r\n");
      out.write("                prevLink.onclick = () => loadPosts(currentPage - 1);\r\n");
      out.write("            } else {\r\n");
      out.write("                prevLink.classList.add('disabled');\r\n");
      out.write("            }\r\n");
      out.write("            pagination.appendChild(prevLink);\r\n");
      out.write("            \r\n");
      out.write("            // 页码\r\n");
      out.write("            const startPage = Math.max(1, currentPage - 2);\r\n");
      out.write("            const endPage = Math.min(totalPages, startPage + 4);\r\n");
      out.write("            \r\n");
      out.write("            for (let i = startPage; i <= endPage; i++) {\r\n");
      out.write("                const pageLink = document.createElement('a');\r\n");
      out.write("                pageLink.href = 'javascript:void(0);';\r\n");
      out.write("                pageLink.textContent = i;\r\n");
      out.write("                pageLink.onclick = () => loadPosts(i);\r\n");
      out.write("                \r\n");
      out.write("                if (i === currentPage) {\r\n");
      out.write("                    pageLink.classList.add('active');\r\n");
      out.write("                }\r\n");
      out.write("                \r\n");
      out.write("                pagination.appendChild(pageLink);\r\n");
      out.write("            }\r\n");
      out.write("            \r\n");
      out.write("            // 下一页\r\n");
      out.write("            const nextLink = document.createElement('a');\r\n");
      out.write("            nextLink.href = 'javascript:void(0);';\r\n");
      out.write("            nextLink.innerHTML = '&raquo;';\r\n");
      out.write("            if (currentPage < totalPages) {\r\n");
      out.write("                nextLink.onclick = () => loadPosts(currentPage + 1);\r\n");
      out.write("            } else {\r\n");
      out.write("                nextLink.classList.add('disabled');\r\n");
      out.write("            }\r\n");
      out.write("            pagination.appendChild(nextLink);\r\n");
      out.write("        }\r\n");
      out.write("        \r\n");
      out.write("        // 加载热门帖子\r\n");
      out.write("        function loadHotPosts() {\r\n");
      out.write("            const hotPosts = document.getElementById('hot-posts');\r\n");
      out.write("            \r\n");
      out.write("            fetch('/community-forum/api/post/list?pageNum=1&pageSize=5&sortBy=viewCount')\r\n");
      out.write("                .then(response => response.json())\r\n");
      out.write("                .then(data => {\r\n");
      out.write("                    if (data.code === 200) {\r\n");
      out.write("                        const posts = data.data.list;\r\n");
      out.write("                        \r\n");
      out.write("                        // 清空加载中提示\r\n");
      out.write("                        hotPosts.innerHTML = '';\r\n");
      out.write("                        \r\n");
      out.write("                        // 添加热门帖子\r\n");
      out.write("                        posts.forEach(post => {\r\n");
      out.write("                            const postElement = document.createElement('div');\r\n");
      out.write("                            postElement.className = 'sidebar-post';\r\n");
      out.write("                            \r\n");
      out.write("                            let postHTML = '';\r\n");
      out.write("                            postHTML += '<a href=\"/community-forum/post/' + post.postId + '\" class=\"sidebar-post-title\">' + post.title + '</a>';\r\n");
      out.write("                            postHTML += '<div class=\"sidebar-post-meta\">';\r\n");
      out.write("                            postHTML += '<span><i class=\"fas fa-eye\"></i> ' + post.viewCount + '</span>';\r\n");
      out.write("                            postHTML += '<span><i class=\"fas fa-thumbs-up\"></i> ' + post.likeCount + '</span>';\r\n");
      out.write("                            postHTML += '</div>';\r\n");
      out.write("                            \r\n");
      out.write("                            postElement.innerHTML = postHTML;\r\n");
      out.write("                            hotPosts.appendChild(postElement);\r\n");
      out.write("                        });\r\n");
      out.write("                    } else {\r\n");
      out.write("                        hotPosts.innerHTML = '<div class=\"error-message\">加载热门帖子失败</div>';\r\n");
      out.write("                    }\r\n");
      out.write("                })\r\n");
      out.write("                .catch(error => {\r\n");
      out.write("                    console.error('加载热门帖子失败:', error);\r\n");
      out.write("                    hotPosts.innerHTML = '<div class=\"error-message\">加载热门帖子失败</div>';\r\n");
      out.write("                });\r\n");
      out.write("        }\r\n");
      out.write("        \r\n");
      out.write("        // 加载分类帖子数\r\n");
      out.write("        function loadCategoryCounts() {\r\n");
      out.write("            fetch('/community-forum/api/category/all')\r\n");
      out.write("                .then(response => response.json())\r\n");
      out.write("                .then(data => {\r\n");
      out.write("                    if (data.code === 200) {\r\n");
      out.write("                        const categories = data.data;\r\n");
      out.write("                        \r\n");
      out.write("                        // 更新分类帖子数\r\n");
      out.write("                        categories.forEach(category => {\r\n");
      out.write("                            const countElement = document.getElementById(`category-count-");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category.categoryId}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("`);\r\n");
      out.write("                            if (countElement) {\r\n");
      out.write("                                countElement.textContent = category.postCount || 0;\r\n");
      out.write("                            }\r\n");
      out.write("                        });\r\n");
      out.write("                    }\r\n");
      out.write("                })\r\n");
      out.write("                .catch(error => console.error('加载分类帖子数失败:', error));\r\n");
      out.write("        }\r\n");
      out.write("        \r\n");
      out.write("        // 加载社区统计信息\r\n");
      out.write("        function loadCommunityStats() {\r\n");
      out.write("            // 这里可以添加获取社区统计信息的API调用\r\n");
      out.write("            // 暂时使用模拟数据\r\n");
      out.write("            document.getElementById('user-count').textContent = '1000+';\r\n");
      out.write("            document.getElementById('post-count').textContent = '5000+';\r\n");
      out.write("            document.getElementById('comment-count').textContent = '10000+';\r\n");
      out.write("        }\r\n");
      out.write("    </script>\r\n");
      out.write("</body>\r\n");
      out.write("</html> ");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_c_005fforEach_005f0(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f0 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    _jspx_th_c_005fforEach_005f0.setPageContext(_jspx_page_context);
    _jspx_th_c_005fforEach_005f0.setParent(null);
    // /WEB-INF/views/index.jsp(109,24) name = items type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
    _jspx_th_c_005fforEach_005f0.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/views/index.jsp(109,24) '${categories}'",_el_expressionfactory.createValueExpression(_jspx_page_context.getELContext(),"${categories}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
    // /WEB-INF/views/index.jsp(109,24) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fforEach_005f0.setVar("category");
    int[] _jspx_push_body_count_c_005fforEach_005f0 = new int[] { 0 };
    try {
      int _jspx_eval_c_005fforEach_005f0 = _jspx_th_c_005fforEach_005f0.doStartTag();
      if (_jspx_eval_c_005fforEach_005f0 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                            <li>\r\n");
          out.write("                                <a href=\"/community-forum/category/");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category.categoryId}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
          out.write("\">\r\n");
          out.write("                                    <i class=\"fas ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category.icon}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
          out.write(" icon\"></i>\r\n");
          out.write("                                    ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category.name}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
          out.write("\r\n");
          out.write("                                    <span class=\"count\" id=\"category-count-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category.categoryId}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
          out.write("\">0</span>\r\n");
          out.write("                                </a>\r\n");
          out.write("                            </li>\r\n");
          out.write("                        ");
          int evalDoAfterBody = _jspx_th_c_005fforEach_005f0.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fforEach_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
    } catch (java.lang.Throwable _jspx_exception) {
      while (_jspx_push_body_count_c_005fforEach_005f0[0]-- > 0)
        out = _jspx_page_context.popBody();
      _jspx_th_c_005fforEach_005f0.doCatch(_jspx_exception);
    } finally {
      _jspx_th_c_005fforEach_005f0.doFinally();
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f0);
    }
    return false;
  }
}
