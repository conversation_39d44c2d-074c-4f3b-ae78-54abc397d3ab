/* 
 * 社区论坛样式表
 * 参考Reddit、Discourse等国外流行社区论坛的布局风格
 */

/* 基础样式 */
:root {
  --primary-color: #1e88e5;
  --primary-light: #e3f2fd;
  --primary-dark: #1565c0;
  --secondary-color: #ff8f00;
  --text-color: #333;
  --text-light: #757575;
  --bg-color: #f5f5f5;
  --card-bg: #ffffff;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --radius: 8px;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--text-color);
  background-color: var(--bg-color);
  line-height: 1.6;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

button, .btn {
  cursor: pointer;
  border: none;
  border-radius: var(--radius);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: var(--transition);
  font-size: 0.9rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #f57c00;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-light);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 头部导航 */
.header {
  background-color: var(--card-bg);
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.search-form {
  position: relative;
  width: 300px;
}

.search-input {
  width: 100%;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  font-size: 0.9rem;
}

.search-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  background-color: var(--card-bg);
  min-width: 160px;
  box-shadow: var(--shadow);
  border-radius: var(--radius);
  z-index: 1;
}

.dropdown-content a {
  padding: 0.75rem 1rem;
  display: block;
  color: var(--text-color);
}

.dropdown-content a:hover {
  background-color: var(--primary-light);
}

.dropdown:hover .dropdown-content {
  display: block;
}

/* 主要内容区 */
.main {
  display: flex;
  gap: 2rem;
  margin: 2rem 0;
}

.content {
  flex: 1;
}

.sidebar {
  width: 300px;
}

/* 帖子卡片 */
.post-card {
  background-color: var(--card-bg);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: 1rem;
  overflow: hidden;
  transition: var(--transition);
}

.post-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.post-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.post-header .avatar {
  margin-right: 0.5rem;
}

.post-meta {
  font-size: 0.85rem;
  color: var(--text-light);
}

.post-category {
  display: inline-block;
  background-color: var(--primary-light);
  color: var(--primary-color);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: 0.5rem;
}

.post-content {
  padding: 1rem;
}

.post-title {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.post-excerpt {
  color: var(--text-light);
  margin-bottom: 1rem;
}

.post-footer {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-top: 1px solid var(--border-color);
  color: var(--text-light);
  font-size: 0.9rem;
}

.post-footer .action {
  display: flex;
  align-items: center;
  margin-right: 1rem;
  cursor: pointer;
}

.post-footer .action i {
  margin-right: 0.25rem;
}

.post-footer .action:hover {
  color: var(--primary-color);
}

/* 侧边栏 */
.sidebar-section {
  background-color: var(--card-bg);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
}

.sidebar-content {
  padding: 1rem;
}

.category-list {
  list-style: none;
}

.category-list li {
  margin-bottom: 0.5rem;
}

.category-list a {
  display: flex;
  align-items: center;
  color: var(--text-color);
  padding: 0.5rem;
  border-radius: var(--radius);
}

.category-list a:hover {
  background-color: var(--primary-light);
}

.category-list .icon {
  margin-right: 0.5rem;
  color: var(--primary-color);
}

.category-list .count {
  margin-left: auto;
  background-color: var(--primary-light);
  color: var(--primary-color);
  padding: 0.1rem 0.5rem;
  border-radius: 10px;
  font-size: 0.75rem;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.pagination a {
  padding: 0.5rem 0.75rem;
  margin: 0 0.25rem;
  border-radius: var(--radius);
  color: var(--text-color);
}

.pagination a:hover {
  background-color: var(--primary-light);
}

.pagination .active {
  background-color: var(--primary-color);
  color: white;
}

/* 表单样式 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  font-size: 1rem;
  transition: var(--transition);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
}

/* 帖子详情页 */
.post-detail {
  background-color: var(--card-bg);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: 1.5rem;
}

.post-detail .post-header {
  padding: 1.5rem;
}

.post-detail .post-title {
  font-size: 1.75rem;
  margin-top: 0.5rem;
}

.post-detail .post-content {
  padding: 1.5rem;
  line-height: 1.8;
}

/* 评论区 */
.comments-section {
  margin-top: 2rem;
}

.comments-header {
  margin-bottom: 1rem;
  font-weight: 600;
}

.comment {
  background-color: var(--card-bg);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: 1rem;
  padding: 1rem;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.comment-header .avatar {
  margin-right: 0.5rem;
}

.comment-meta {
  font-size: 0.85rem;
  color: var(--text-light);
}

.comment-content {
  margin-left: 2.5rem;
}

.comment-footer {
  margin-top: 0.5rem;
  margin-left: 2.5rem;
  display: flex;
  align-items: center;
  color: var(--text-light);
  font-size: 0.85rem;
}

.comment-footer .action {
  margin-right: 1rem;
  cursor: pointer;
}

.comment-footer .action:hover {
  color: var(--primary-color);
}

.reply-form {
  margin-top: 1rem;
  margin-left: 2.5rem;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .main {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    order: -1;
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 768px) {
  .search-form {
    width: 200px;
  }
  
  .post-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .post-meta {
    margin-top: 0.5rem;
  }
}

@media (max-width: 576px) {
  .navbar {
    flex-direction: column;
    gap: 1rem;
  }
  
  .search-form {
    width: 100%;
  }
  
  .nav-links {
    width: 100%;
    justify-content: space-between;
  }
}

/* 工具类 */
.text-center {
  text-align: center;
}

.mt-1 {
  margin-top: 0.5rem;
}

.mt-2 {
  margin-top: 1rem;
}

.mt-3 {
  margin-top: 1.5rem;
}

.mb-1 {
  margin-bottom: 0.5rem;
}

.mb-2 {
  margin-bottom: 1rem;
}

.mb-3 {
  margin-bottom: 1.5rem;
}

/* 通知样式 */
.notification-badge {
  position: relative;
}

.notification-badge .badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: var(--error-color);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background-color: var(--primary-light);
}

.notification-content {
  font-size: 0.9rem;
}

.notification-time {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-top: 0.25rem;
} 